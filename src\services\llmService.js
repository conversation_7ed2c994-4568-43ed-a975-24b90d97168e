const { EventEmitter } = require('events');
const OpenAI = require('openai');
const Anthropic = require('@anthropic-ai/sdk');
const axios = require('axios');

class LLMService extends EventEmitter {
    constructor() {
        super();
        this.providers = {
            openai: null,
            anthropic: null,
            custom: null
        };
        this.currentProvider = 'openai'; // Default provider
        this.conversationHistory = [];
        this.systemPrompts = {};
        
        this.initializeProviders();
        this.loadSystemPrompts();
    }

    initializeProviders() {
        try {
            // Initialize OpenAI
            if (process.env.OPENAI_API_KEY) {
                this.providers.openai = new OpenAI({
                    apiKey: process.env.OPENAI_API_KEY,
                });
                console.log('OpenAI provider initialized');
            }

            // Initialize Anthropic
            if (process.env.ANTHROPIC_API_KEY) {
                this.providers.anthropic = new Anthropic({
                    apiKey: process.env.ANTHROPIC_API_KEY,
                });
                console.log('Anthropic provider initialized');
            }

            // Set default provider based on available keys
            if (this.providers.openai) {
                this.currentProvider = 'openai';
            } else if (this.providers.anthropic) {
                this.currentProvider = 'anthropic';
            }

            this.emit('status', `LLM Service ready with ${this.currentProvider} provider`);
        } catch (error) {
            console.error('Failed to initialize LLM providers:', error);
            this.emit('error', `LLM initialization failed: ${error.message}`);
        }
    }

    loadSystemPrompts() {
        // Load system prompts for different profiles/contexts
        this.systemPrompts = {
            interview: `You are an AI assistant helping with interview preparation. Provide concise, accurate, and helpful responses to interview questions. Focus on technical accuracy and practical advice.`,
            
            'sales-call': `You are an AI assistant helping with sales conversations. Provide strategic advice, objection handling techniques, and conversation guidance to help close deals effectively.`,
            
            'business-meeting': `You are an AI assistant helping with business meetings. Provide insights, suggestions, and strategic advice to help navigate professional discussions successfully.`,
            
            presentation: `You are an AI assistant helping with presentations. Provide guidance on content structure, delivery techniques, and audience engagement strategies.`,
            
            negotiation: `You are an AI assistant helping with negotiations. Provide strategic advice, tactics, and communication techniques to achieve favorable outcomes.`,
            
            default: `You are a helpful AI assistant. Provide accurate, concise, and contextually relevant responses to user queries.`
        };
    }

    setProvider(provider) {
        if (this.providers[provider]) {
            this.currentProvider = provider;
            console.log(`Switched to ${provider} provider`);
            this.emit('provider-changed', provider);
            return true;
        }
        return false;
    }

    updateApiKey(provider, apiKey) {
        try {
            switch (provider) {
                case 'openai':
                    this.providers.openai = new OpenAI({ apiKey });
                    break;
                case 'anthropic':
                    this.providers.anthropic = new Anthropic({ apiKey });
                    break;
                default:
                    throw new Error(`Unknown provider: ${provider}`);
            }
            
            console.log(`Updated API key for ${provider}`);
            this.emit('api-key-updated', provider);
            return true;
        } catch (error) {
            console.error(`Failed to update API key for ${provider}:`, error);
            return false;
        }
    }

    async processText(text, profile = 'default', conversationHistory = []) {
        const startTime = Date.now();
        
        try {
            if (!this.providers[this.currentProvider]) {
                throw new Error(`${this.currentProvider} provider not initialized`);
            }

            const systemPrompt = this.systemPrompts[profile] || this.systemPrompts.default;
            const response = await this._callProvider(text, systemPrompt, conversationHistory);
            
            const processingTime = Date.now() - startTime;
            
            // Add to conversation history
            this.conversationHistory.push({
                timestamp: Date.now(),
                input: text,
                output: response,
                profile,
                provider: this.currentProvider,
                processingTime
            });

            console.log(`LLM processing completed in ${processingTime}ms`);
            
            return {
                response,
                metadata: {
                    provider: this.currentProvider,
                    profile,
                    processingTime,
                    usedFallback: false
                }
            };
        } catch (error) {
            console.error('LLM processing failed:', error);
            
            // Try fallback provider
            const fallbackResponse = await this._tryFallback(text, profile, conversationHistory);
            if (fallbackResponse) {
                return fallbackResponse;
            }
            
            throw error;
        }
    }

    async _callProvider(text, systemPrompt, conversationHistory = []) {
        switch (this.currentProvider) {
            case 'openai':
                return await this._callOpenAI(text, systemPrompt, conversationHistory);
            case 'anthropic':
                return await this._callAnthropic(text, systemPrompt, conversationHistory);
            default:
                throw new Error(`Unsupported provider: ${this.currentProvider}`);
        }
    }

    async _callOpenAI(text, systemPrompt, conversationHistory = []) {
        const messages = [
            { role: 'system', content: systemPrompt }
        ];

        // Add conversation history
        conversationHistory.forEach(item => {
            if (item.input) {
                messages.push({ role: 'user', content: item.input });
            }
            if (item.output) {
                messages.push({ role: 'assistant', content: item.output });
            }
        });

        // Add current message
        messages.push({ role: 'user', content: text });

        const completion = await this.providers.openai.chat.completions.create({
            model: 'gpt-4o',
            messages: messages,
            max_tokens: 2000,
            temperature: 0.7,
        });

        return completion.choices[0].message.content;
    }

    async _callAnthropic(text, systemPrompt, conversationHistory = []) {
        const messages = [];

        // Add conversation history
        conversationHistory.forEach(item => {
            if (item.input) {
                messages.push({ role: 'user', content: item.input });
            }
            if (item.output) {
                messages.push({ role: 'assistant', content: item.output });
            }
        });

        // Add current message
        messages.push({ role: 'user', content: text });

        const response = await this.providers.anthropic.messages.create({
            model: 'claude-3-5-sonnet-20241022',
            max_tokens: 2000,
            system: systemPrompt,
            messages: messages,
        });

        return response.content[0].text;
    }

    async _tryFallback(text, profile, conversationHistory) {
        const availableProviders = Object.keys(this.providers).filter(
            p => this.providers[p] && p !== this.currentProvider
        );

        for (const provider of availableProviders) {
            try {
                console.log(`Trying fallback provider: ${provider}`);
                const originalProvider = this.currentProvider;
                this.currentProvider = provider;
                
                const systemPrompt = this.systemPrompts[profile] || this.systemPrompts.default;
                const response = await this._callProvider(text, systemPrompt, conversationHistory);
                
                // Restore original provider
                this.currentProvider = originalProvider;
                
                return {
                    response,
                    metadata: {
                        provider: provider,
                        profile,
                        processingTime: 0,
                        usedFallback: true,
                        fallbackReason: 'Primary provider failed'
                    }
                };
            } catch (fallbackError) {
                console.error(`Fallback provider ${provider} also failed:`, fallbackError);
                continue;
            }
        }

        return null;
    }

    async processTranscription(transcription, profile = 'default', conversationHistory = []) {
        // Enhanced processing for transcribed speech
        const enhancedPrompt = `The following is a transcribed speech that may contain errors or unclear parts. Please provide a helpful and contextually appropriate response:

Transcription: "${transcription}"

Please respond as if you're having a natural conversation, taking into account that this is spoken input that may have transcription errors.`;

        return await this.processText(enhancedPrompt, profile, conversationHistory);
    }

    async analyzeScreenContent(ocrText, profile = 'default', conversationHistory = []) {
        // Enhanced processing for OCR extracted text
        const enhancedPrompt = `The following text was extracted from a screen capture using OCR. It may contain formatting issues or recognition errors. Please analyze the content and provide helpful insights or answers:

Screen Content: "${ocrText}"

Please provide relevant assistance based on what you can understand from this screen content.`;

        return await this.processText(enhancedPrompt, profile, conversationHistory);
    }

    getAvailableProviders() {
        return Object.keys(this.providers).filter(p => this.providers[p]);
    }

    getCurrentProvider() {
        return this.currentProvider;
    }

    getStats() {
        return {
            currentProvider: this.currentProvider,
            availableProviders: this.getAvailableProviders(),
            conversationHistoryLength: this.conversationHistory.length,
            isInitialized: this.getAvailableProviders().length > 0
        };
    }

    clearHistory() {
        this.conversationHistory = [];
    }

    getConversationHistory() {
        return this.conversationHistory;
    }

    async testConnection(provider = null) {
        const testProvider = provider || this.currentProvider;
        
        if (!this.providers[testProvider]) {
            return {
                success: false,
                message: `${testProvider} provider not initialized`
            };
        }

        try {
            const testResponse = await this._callProvider(
                'Hello, this is a connection test.',
                'You are a helpful assistant. Respond briefly to confirm the connection is working.',
                []
            );

            return {
                success: true,
                message: 'Connection test successful',
                provider: testProvider,
                response: testResponse
            };
        } catch (error) {
            return {
                success: false,
                message: error.message,
                provider: testProvider
            };
        }
    }

    setSystemPrompt(profile, prompt) {
        this.systemPrompts[profile] = prompt;
        console.log(`Updated system prompt for profile: ${profile}`);
    }

    getSystemPrompt(profile) {
        return this.systemPrompts[profile] || this.systemPrompts.default;
    }
}

module.exports = new LLMService();
