const { EventEmitter } = require('events');

class SessionService extends EventEmitter {
    constructor() {
        super();
        this.currentSession = null;
        this.sessions = new Map();
        this.maxHistoryLength = 50; // Maximum number of conversation turns to keep
        this.maxSessionAge = 24 * 60 * 60 * 1000; // 24 hours in milliseconds
        
        this.initializeSession();
        this.startCleanupTimer();
    }

    initializeSession() {
        this.currentSession = {
            id: this.generateSessionId(),
            startTime: Date.now(),
            lastActivity: Date.now(),
            profile: 'default',
            conversationHistory: [],
            metadata: {
                totalInteractions: 0,
                speechInputs: 0,
                ocrInputs: 0,
                llmResponses: 0
            }
        };

        this.sessions.set(this.currentSession.id, this.currentSession);
        console.log('New session initialized:', this.currentSession.id);
        this.emit('session-started', this.currentSession);
    }

    generateSessionId() {
        return `session_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    }

    addUserInput(text, inputType = 'text', metadata = {}) {
        if (!this.currentSession) {
            this.initializeSession();
        }

        const entry = {
            id: this.generateEntryId(),
            timestamp: Date.now(),
            type: 'user_input',
            inputType, // 'speech', 'text', 'ocr'
            content: text,
            metadata
        };

        this.currentSession.conversationHistory.push(entry);
        this.currentSession.lastActivity = Date.now();
        this.currentSession.metadata.totalInteractions++;

        // Update specific counters
        if (inputType === 'speech') {
            this.currentSession.metadata.speechInputs++;
        } else if (inputType === 'ocr') {
            this.currentSession.metadata.ocrInputs++;
        }

        this.trimHistoryIfNeeded();
        this.emit('user-input-added', entry);

        console.log(`Added user input (${inputType}):`, text.substring(0, 100) + '...');
        return entry;
    }

    addLLMResponse(response, metadata = {}) {
        if (!this.currentSession) {
            this.initializeSession();
        }

        const entry = {
            id: this.generateEntryId(),
            timestamp: Date.now(),
            type: 'llm_response',
            content: response,
            metadata
        };

        this.currentSession.conversationHistory.push(entry);
        this.currentSession.lastActivity = Date.now();
        this.currentSession.metadata.llmResponses++;

        this.trimHistoryIfNeeded();
        this.emit('llm-response-added', entry);

        console.log('Added LLM response:', response.substring(0, 100) + '...');
        return entry;
    }

    addSystemEvent(event, metadata = {}) {
        if (!this.currentSession) {
            this.initializeSession();
        }

        const entry = {
            id: this.generateEntryId(),
            timestamp: Date.now(),
            type: 'system_event',
            event,
            metadata
        };

        this.currentSession.conversationHistory.push(entry);
        this.currentSession.lastActivity = Date.now();

        this.emit('system-event-added', entry);
        return entry;
    }

    generateEntryId() {
        return `entry_${Date.now()}_${Math.random().toString(36).substr(2, 6)}`;
    }

    trimHistoryIfNeeded() {
        if (this.currentSession.conversationHistory.length > this.maxHistoryLength) {
            const removed = this.currentSession.conversationHistory.splice(
                0, 
                this.currentSession.conversationHistory.length - this.maxHistoryLength
            );
            console.log(`Trimmed ${removed.length} old conversation entries`);
        }
    }

    getConversationHistory(limit = null) {
        if (!this.currentSession) {
            return [];
        }

        const history = this.currentSession.conversationHistory;
        return limit ? history.slice(-limit) : history;
    }

    getOptimizedHistory(maxEntries = 20) {
        const history = this.getConversationHistory();
        
        // Get recent entries, prioritizing user inputs and LLM responses
        const relevantEntries = history
            .filter(entry => entry.type === 'user_input' || entry.type === 'llm_response')
            .slice(-maxEntries);

        // Format for LLM consumption
        const formatted = relevantEntries.map(entry => {
            if (entry.type === 'user_input') {
                return {
                    role: 'user',
                    content: entry.content,
                    timestamp: entry.timestamp,
                    inputType: entry.inputType
                };
            } else if (entry.type === 'llm_response') {
                return {
                    role: 'assistant',
                    content: entry.content,
                    timestamp: entry.timestamp,
                    provider: entry.metadata?.provider
                };
            }
        }).filter(Boolean);

        return {
            recent: formatted,
            totalEntries: history.length,
            sessionId: this.currentSession.id
        };
    }

    setProfile(profile) {
        if (this.currentSession) {
            this.currentSession.profile = profile;
            this.addSystemEvent('profile_changed', { newProfile: profile });
            console.log('Session profile updated to:', profile);
            this.emit('profile-changed', profile);
        }
    }

    getProfile() {
        return this.currentSession?.profile || 'default';
    }

    getCurrentSession() {
        return this.currentSession;
    }

    getSessionStats() {
        if (!this.currentSession) {
            return null;
        }

        const duration = Date.now() - this.currentSession.startTime;
        const timeSinceLastActivity = Date.now() - this.currentSession.lastActivity;

        return {
            sessionId: this.currentSession.id,
            duration,
            timeSinceLastActivity,
            profile: this.currentSession.profile,
            ...this.currentSession.metadata,
            historyLength: this.currentSession.conversationHistory.length
        };
    }

    clearCurrentSession() {
        if (this.currentSession) {
            const oldSessionId = this.currentSession.id;
            this.addSystemEvent('session_cleared');
            
            // Archive current session
            this.sessions.set(oldSessionId, { 
                ...this.currentSession, 
                endTime: Date.now(),
                status: 'cleared'
            });

            // Start new session
            this.initializeSession();
            
            console.log('Session cleared and new session started');
            this.emit('session-cleared', oldSessionId);
        }
    }

    switchToSession(sessionId) {
        if (this.sessions.has(sessionId)) {
            // Archive current session
            if (this.currentSession) {
                this.sessions.set(this.currentSession.id, this.currentSession);
            }

            // Switch to requested session
            this.currentSession = this.sessions.get(sessionId);
            this.currentSession.lastActivity = Date.now();

            console.log('Switched to session:', sessionId);
            this.emit('session-switched', sessionId);
            return true;
        }
        return false;
    }

    getAllSessions() {
        return Array.from(this.sessions.values()).map(session => ({
            id: session.id,
            startTime: session.startTime,
            endTime: session.endTime,
            lastActivity: session.lastActivity,
            profile: session.profile,
            status: session.status || 'active',
            metadata: session.metadata
        }));
    }

    deleteSession(sessionId) {
        if (sessionId === this.currentSession?.id) {
            this.clearCurrentSession();
        } else {
            this.sessions.delete(sessionId);
            console.log('Deleted session:', sessionId);
            this.emit('session-deleted', sessionId);
        }
    }

    startCleanupTimer() {
        // Clean up old sessions every hour
        setInterval(() => {
            this.cleanupOldSessions();
        }, 60 * 60 * 1000);
    }

    cleanupOldSessions() {
        const now = Date.now();
        let cleanedCount = 0;

        for (const [sessionId, session] of this.sessions.entries()) {
            // Skip current session
            if (sessionId === this.currentSession?.id) {
                continue;
            }

            // Remove sessions older than maxSessionAge
            if (now - session.lastActivity > this.maxSessionAge) {
                this.sessions.delete(sessionId);
                cleanedCount++;
            }
        }

        if (cleanedCount > 0) {
            console.log(`Cleaned up ${cleanedCount} old sessions`);
            this.emit('sessions-cleaned', cleanedCount);
        }
    }

    exportSession(sessionId = null) {
        const targetSession = sessionId ? 
            this.sessions.get(sessionId) : 
            this.currentSession;

        if (!targetSession) {
            return null;
        }

        return {
            ...targetSession,
            exportTime: Date.now(),
            version: '1.0'
        };
    }

    importSession(sessionData) {
        try {
            if (!sessionData.id || !sessionData.conversationHistory) {
                throw new Error('Invalid session data format');
            }

            // Generate new ID to avoid conflicts
            const newSessionId = this.generateSessionId();
            const importedSession = {
                ...sessionData,
                id: newSessionId,
                importTime: Date.now(),
                lastActivity: Date.now()
            };

            this.sessions.set(newSessionId, importedSession);
            console.log('Session imported with new ID:', newSessionId);
            this.emit('session-imported', newSessionId);

            return newSessionId;
        } catch (error) {
            console.error('Failed to import session:', error);
            return null;
        }
    }

    getMemoryUsage() {
        const totalSessions = this.sessions.size;
        let totalEntries = 0;
        let totalSize = 0;

        for (const session of this.sessions.values()) {
            totalEntries += session.conversationHistory.length;
            totalSize += JSON.stringify(session).length;
        }

        return {
            totalSessions,
            totalEntries,
            approximateSize: `${(totalSize / 1024).toFixed(2)} KB`,
            currentSessionEntries: this.currentSession?.conversationHistory.length || 0
        };
    }
}

module.exports = new SessionService();
