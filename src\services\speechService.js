const { EventEmitter } = require('events');
const sdk = require('microsoft-cognitiveservices-speech-sdk');
const recorder = require('node-record-lpcm16');

class SpeechService extends EventEmitter {
    constructor() {
        super();
        this.recognizer = null;
        this.isRecording = false;
        this.audioConfig = null;
        this.speechConfig = null;
        this.sessionStartTime = null;
        this.retryCount = 0;
        this.maxRetries = 3;
        this.pushStream = null;
        this.recording = null;
        this.conversationHistory = [];
        this.currentSessionId = null;
        
        this.initializeClient();
    }

    initializeClient() {
        try {
            // Get Azure Speech credentials from environment variables
            const subscriptionKey = process.env.AZURE_SPEECH_KEY;
            const region = process.env.AZURE_SPEECH_REGION;

            if (!subscriptionKey || !region) {
                const error = 'Azure Speech credentials not found. Please set AZURE_SPEECH_KEY and AZURE_SPEECH_REGION environment variables.';
                console.warn('Speech service initialization failed:', error);
                // Don't emit error to prevent unhandled error crashes
                this.speechConfig = null;
                return;
            }

            // Initialize Azure Speech configuration
            this.speechConfig = sdk.SpeechConfig.fromSubscription(subscriptionKey, region);
            this.speechConfig.speechRecognitionLanguage = 'en-US';
            this.speechConfig.outputFormat = sdk.OutputFormat.Detailed;

            // Set properties for better recognition
            this.speechConfig.setProperty(sdk.PropertyId.SpeechServiceConnection_InitialSilenceTimeoutMs, "5000");
            this.speechConfig.setProperty(sdk.PropertyId.SpeechServiceConnection_EndSilenceTimeoutMs, "2000");
            this.speechConfig.setProperty(sdk.PropertyId.Speech_SegmentationSilenceTimeoutMs, "2000");

            console.log('Azure Speech service initialized successfully');
            this.emit('status', 'Azure Speech Services ready');
        } catch (error) {
            console.warn('Failed to initialize Azure Speech client:', error);
            this.speechConfig = null;
        }
    }

    startRecording() {
        try {
            if (!this.speechConfig) {
                const errorMsg = 'Azure Speech client not initialized';
                console.error(errorMsg);
                this.emit('error', errorMsg);
                return;
            }

            if (this.isRecording) {
                console.warn('Recording already in progress');
                return;
            }

            this.sessionStartTime = Date.now();
            this.currentSessionId = Date.now().toString();
            this.retryCount = 0;
            this._attemptRecording();
        } catch (error) {
            console.error('Critical error in startRecording:', error);
            this.emit('error', `Speech recognition failed to start: ${error.message}`);
            this.isRecording = false;
        }
    }

    _attemptRecording() {
        try {
            this.isRecording = true;
            this.emit('recording-started');

            // Clean up any existing resources
            this._cleanup();

            // Create push stream for audio input
            this.pushStream = sdk.AudioInputStream.createPushStream();
            this.audioConfig = sdk.AudioConfig.fromStreamInput(this.pushStream);

            // Start capturing microphone audio
            this._startMicrophoneCapture();

            // Create speech recognizer
            this.recognizer = new sdk.SpeechRecognizer(this.speechConfig, this.audioConfig);

            // Set up event handlers
            this._setupRecognizerEvents();

            // Start continuous recognition
            this._startContinuousRecognition();

        } catch (error) {
            console.error('Failed to start recording session:', error);
            this.emit('error', `Recording startup failed: ${error.message}`);
            this.isRecording = false;
        }
    }

    _setupRecognizerEvents() {
        // Interim results (real-time transcription)
        this.recognizer.recognizing = (s, e) => {
            try {
                if (e.result.reason === sdk.ResultReason.RecognizingSpeech) {
                    console.log('Interim transcription:', e.result.text);
                    this.emit('interim-transcription', e.result.text);
                }
            } catch (error) {
                console.error('Error in recognizing handler:', error);
            }
        };

        // Final results
        this.recognizer.recognized = (s, e) => {
            try {
                if (e.result.reason === sdk.ResultReason.RecognizedSpeech) {
                    const sessionDuration = Date.now() - this.sessionStartTime;
                    
                    if (e.result.text && e.result.text.trim().length > 0) {
                        console.log('Final transcription:', e.result.text);
                        
                        // Add to conversation history
                        this.conversationHistory.push({
                            timestamp: Date.now(),
                            text: e.result.text,
                            type: 'transcription',
                            sessionId: this.currentSessionId,
                            duration: sessionDuration
                        });

                        this.emit('transcription', e.result.text);
                    }
                } else if (e.result.reason === sdk.ResultReason.NoMatch) {
                    console.log('No speech pattern detected in audio');
                }
            } catch (error) {
                console.error('Error in recognized handler:', error);
            }
        };

        // Handle cancellation/errors
        this.recognizer.canceled = (s, e) => {
            console.warn('Recognition session canceled:', e.reason, e.errorDetails);
            
            if (e.reason === sdk.CancellationReason.Error) {
                this._handleRecognitionError(e);
            }
            
            this.stopRecording();
        };

        // Session events
        this.recognizer.sessionStarted = (s, e) => {
            console.log('Recognition session started:', e.sessionId);
        };

        this.recognizer.sessionStopped = (s, e) => {
            console.log('Recognition session ended:', e.sessionId);
            this.stopRecording();
        };
    }

    _handleRecognitionError(e) {
        let errorMsg = `Recognition error: ${e.errorDetails}`;
        
        // Provide specific error messages
        if (e.errorDetails.includes('1006')) {
            errorMsg = 'Network connection failed. Please check your internet connection.';
        } else if (e.errorDetails.includes('InvalidServiceCredentials')) {
            errorMsg = 'Invalid Azure Speech credentials. Please check AZURE_SPEECH_KEY and AZURE_SPEECH_REGION.';
        } else if (e.errorDetails.includes('Forbidden')) {
            errorMsg = 'Access denied. Please check your Azure Speech service subscription and region.';
        }

        this.emit('error', errorMsg);

        // Attempt retry for transient errors
        if (this.retryCount < this.maxRetries && this._isTransientError(e.errorDetails)) {
            this.retryCount++;
            console.log(`Retrying recognition (attempt ${this.retryCount}/${this.maxRetries})`);
            setTimeout(() => {
                if (!this.isRecording) {
                    this._attemptRecording();
                }
            }, 1000 * this.retryCount);
        }
    }

    _isTransientError(errorDetails) {
        return errorDetails.includes('1006') || 
               errorDetails.includes('timeout') || 
               errorDetails.includes('network');
    }

    _startContinuousRecognition() {
        const startTimeout = setTimeout(() => {
            console.error('Recognition start timeout');
            this.emit('error', 'Speech recognition start timeout. Please try again.');
            this.stopRecording();
        }, 10000);

        this.recognizer.startContinuousRecognitionAsync(
            () => {
                clearTimeout(startTimeout);
                console.log('Continuous speech recognition started successfully');
            },
            (error) => {
                clearTimeout(startTimeout);
                console.error('Failed to start continuous recognition:', error);
                
                if (this.retryCount < this.maxRetries) {
                    this.retryCount++;
                    console.log(`Retrying recognition start (attempt ${this.retryCount}/${this.maxRetries})`);
                    this.isRecording = false;
                    setTimeout(() => {
                        this._attemptRecording();
                    }, 2000 * this.retryCount);
                } else {
                    this.emit('error', `Recognition startup failed after ${this.maxRetries} attempts: ${error}`);
                    this.isRecording = false;
                }
            }
        );
    }

    _startMicrophoneCapture() {
        if (!this.pushStream) return;

        try {
            // Configure audio recording
            this.recording = recorder.record({
                sampleRateHertz: 16000, // Azure Speech SDK prefers 16kHz
                threshold: 0,
                verbose: false,
                recordProgram: 'sox', // Use sox for audio recording
                silence: '10.0s'
            });

            if (!this.recording) {
                throw new Error('Failed to create audio recording instance');
            }

            // Handle recording stream errors
            this.recording.stream().on('error', (error) => {
                console.error('Audio recording stream error:', error);
                this._handleAudioError(error);
            });

            // Pipe audio data to Azure Speech SDK
            this.recording.stream().on('data', (chunk) => {
                if (this.pushStream && this.isRecording) {
                    try {
                        this.pushStream.write(chunk);
                    } catch (error) {
                        console.error('Error writing audio data:', error);
                    }
                }
            });

        } catch (error) {
            console.error('Failed to start microphone capture:', error);
            this.emit('error', `Microphone capture failed: ${error.message}`);
        }
    }

    _handleAudioError(error) {
        // Try to restart recording
        if (this.recording) {
            try {
                this.recording.stop();
            } catch (stopError) {
                console.error('Error stopping recording:', stopError);
            }
            this.recording = null;
        }

        // Retry with fallback
        setTimeout(() => {
            if (this.isRecording) {
                this._startMicrophoneCapture();
            }
        }, 1000);
    }

    stopRecording() {
        if (!this.isRecording) {
            return;
        }

        this.isRecording = false;
        const sessionDuration = this.sessionStartTime ? Date.now() - this.sessionStartTime : 0;
        console.log('Stopping speech recognition session, duration:', sessionDuration + 'ms');

        // Stop continuous recognition
        if (this.recognizer) {
            try {
                this.recognizer.stopContinuousRecognitionAsync(
                    () => {
                        console.log('Speech recognition stopped successfully');
                        this.emit('recording-stopped');
                        this.emit('status', 'Recording stopped');
                        this._cleanup();
                    },
                    (error) => {
                        console.error('Error during recognition stop:', error);
                        this._cleanup();
                    }
                );
            } catch (error) {
                console.error('Error stopping recognizer:', error);
                this._cleanup();
            }
        } else {
            this._cleanup();
        }
    }

    _cleanup() {
        // Clean up recognizer
        if (this.recognizer) {
            try {
                this.recognizer.close();
            } catch (error) {
                console.error('Error closing recognizer:', error);
            }
            this.recognizer = null;
        }

        // Clean up audio config
        if (this.audioConfig) {
            try {
                if (typeof this.audioConfig.close === 'function') {
                    this.audioConfig.close();
                }
            } catch (error) {
                console.error('Error closing audio config:', error);
            }
            this.audioConfig = null;
        }

        // Stop audio recording
        if (this.recording) {
            try {
                this.recording.stop();
                this.recording = null;
            } catch (error) {
                console.error('Error stopping audio recording:', error);
            }
        }

        // Clean up push stream
        if (this.pushStream) {
            try {
                if (typeof this.pushStream.close === 'function') {
                    this.pushStream.close();
                }
            } catch (error) {
                console.error('Error closing push stream:', error);
            }
            this.pushStream = null;
        }
    }

    getStatus() {
        return {
            isRecording: this.isRecording,
            isInitialized: !!this.speechConfig,
            sessionDuration: this.sessionStartTime ? Date.now() - this.sessionStartTime : 0,
            retryCount: this.retryCount,
            currentSessionId: this.currentSessionId,
            conversationHistoryLength: this.conversationHistory.length
        };
    }

    getConversationHistory() {
        return this.conversationHistory;
    }

    clearHistory() {
        this.conversationHistory = [];
        this.currentSessionId = null;
    }

    // Test connection method
    async testConnection() {
        if (!this.speechConfig) {
            throw new Error('Speech service not initialized');
        }

        try {
            // Create a simple test recognizer
            const audioConfig = sdk.AudioConfig.fromDefaultMicrophoneInput();
            const recognizer = new sdk.SpeechRecognizer(this.speechConfig, audioConfig);

            // Test by attempting to create the recognizer
            recognizer.close();
            audioConfig.close();

            return {
                success: true,
                message: 'Connection test successful'
            };
        } catch (error) {
            return {
                success: false,
                message: error.message
            };
        }
    }
}

module.exports = new SpeechService();
