public interface IUserRepository {
    Map<Id, User> getUsersByIds(Set<Id> userIds);
}

public class UserRepository implements IUserRepository {
    public Map<Id, User> getUsersByIds(Set<Id> userIds) {
        return new Map<Id, User>([
            SELECT Id, ManagerId
            FROM User
            WHERE Id IN :userIds
        ]);
    }
}

public interface IQueueProvider {
    Id getDefaultQueueId();
}

public class QueueProvider implements IQueueProvider {
    public Id getDefaultQueueId() {
        List<Group> q = [
            SELECT Id
            FROM Group
            WHERE DeveloperName = 'Default_Project_Name' AND Type = 'Queue'
            LIMIT 1
        ];
        if (q.isEmpty()) {
            throw new ProjectSetupException('Default Project Queue not found.');
        }
        return q[0].Id;
    }
}

public class OpportunityFilter {
    public static List<Opportunity> filterClosedWon(
        List<Opportunity> newList,
        Map<Id, Opportunity> oldMap
    ) {
        List<Opportunity> result = new List<Opportunity>();
        for (Opportunity opp : newList) {
            Opportunity oldOpp = oldMap.get(opp.Id);
            if (oldOpp != null &&
                opp.StageName == 'Closed Won' &&
                oldOpp.StageName != 'Closed Won') {
                result.add(opp);
            }
        }
        return result;
    }
}

public class ProjectBuilder {
    private final Id defaultQueueId;
    private final Map<Id, User> userMap;

    public ProjectBuilder(Id defaultQueueId, Map<Id, User> userMap) {
        this.defaultQueueId = defaultQueueId;
        this.userMap = userMap;
    }

    public Project__c buildFromOpportunity(Opportunity opp) {
        Id ownerId = defaultQueueId;
        User u = userMap.get(opp.OwnerId);
        if (u != null && u.ManagerId != null) {
            ownerId = u.ManagerId;
        }
        return new Project__c(
            Name            = opp.Name,
            Opportunity__c  = opp.Id,
            Start_Date__c   = opp.CloseDate,
            Budget__c       = opp.Amount,
            OwnerId         = ownerId
        );
    }
}

/* Service class – the entry point used by the trigger */
public with sharing class OpportunityTriggerService {
    private final IUserRepository userRepo;
    private final IQueueProvider queueProvider;
    private Boolean isExecuted = false;   // instance‑level flag

    public OpportunityTriggerService() {
        this(new UserRepository(), new QueueProvider());
    }

    // Constructor for test injection
    public OpportunityTriggerService(IUserRepository repo, IQueueProvider provider) {
        this.userRepo = repo;
        this.queueProvider = provider;
    }

    public List<Project__c> createProjectsForClosedWonOpportunities(
        List<Opportunity> newList,
        Map<Id, Opportunity> oldMap
    ) {
        if (isExecuted) {
            return new List<Project__c>();
        }
        isExecuted = true;

        List<Opportunity> closedWon = OpportunityFilter.filterClosedWon(newList, oldMap);
        if (closedWon.isEmpty()) {
            return new List<Project__c>();
        }

        // collect owner Ids
        Set<Id> ownerIds = new Set<Id>();
        for (Opportunity o : closedWon) {
            ownerIds.add(o.OwnerId);
        }

        Map<Id, User> users = userRepo.getUsersByIds(ownerIds);
        Id defaultQueue = queueProvider.getDefaultQueueId();

        ProjectBuilder builder = new ProjectBuilder(defaultQueue, users);
        List<Project__c> toInsert = new List<Project__c>();
        for (Opportunity o : closedWon) {
            toInsert.add(builder.buildFromOpportunity(o));
        }
        return toInsert;
    }
}

/* Custom exception for clearer error handling */
public class ProjectSetupException extends Exception {}
