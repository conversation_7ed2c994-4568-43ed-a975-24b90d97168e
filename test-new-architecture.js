#!/usr/bin/env node

/**
 * Test script for the new Cluely-inspired architecture
 * This script tests the individual services without running the full Electron app
 */

require('dotenv').config();

const speechService = require('./src/services/speechService');
const llmService = require('./src/services/llmService');
const ocrService = require('./src/services/ocrService');
const sessionService = require('./src/services/sessionService');

async function testServices() {
    console.log('🚀 Testing New AI Architecture Services\n');

    // Test 1: Session Service
    console.log('1️⃣ Testing Session Service...');
    try {
        const sessionStats = sessionService.getSessionStats();
        console.log('✅ Session service initialized:', sessionStats);
        
        // Add a test interaction
        sessionService.addUserInput('Hello, this is a test', 'text');
        sessionService.addLLMResponse('Hello! I received your test message.');
        
        const history = sessionService.getConversationHistory();
        console.log(`✅ Session history contains ${history.length} entries`);
    } catch (error) {
        console.error('❌ Session service test failed:', error.message);
    }

    // Test 2: LLM Service
    console.log('\n2️⃣ Testing LLM Service...');
    try {
        const llmStats = llmService.getStats();
        console.log('✅ LLM service stats:', llmStats);
        
        if (llmStats.isInitialized) {
            console.log('🔄 Testing LLM connection...');
            const connectionTest = await llmService.testConnection();
            if (connectionTest.success) {
                console.log('✅ LLM connection test successful');
                
                // Test a simple query
                console.log('🔄 Testing LLM query...');
                const response = await llmService.processText('What is 2+2?', 'default');
                console.log('✅ LLM response received:', response.response.substring(0, 100) + '...');
            } else {
                console.log('⚠️ LLM connection test failed:', connectionTest.message);
            }
        } else {
            console.log('⚠️ LLM service not initialized (missing API keys)');
        }
    } catch (error) {
        console.error('❌ LLM service test failed:', error.message);
    }

    // Test 3: OCR Service
    console.log('\n3️⃣ Testing OCR Service...');
    try {
        const ocrStatus = ocrService.getStatus();
        console.log('✅ OCR service status:', ocrStatus);
        
        if (ocrStatus.isInitialized) {
            console.log('✅ OCR service is ready');
            // Note: We can't test screen capture in a headless environment
            console.log('ℹ️ Screen capture test skipped (requires GUI environment)');
        } else {
            console.log('⚠️ OCR service not initialized');
        }
    } catch (error) {
        console.error('❌ OCR service test failed:', error.message);
    }

    // Test 4: Speech Service
    console.log('\n4️⃣ Testing Speech Service...');
    try {
        const speechStatus = speechService.getStatus();
        console.log('✅ Speech service status:', speechStatus);
        
        if (speechStatus.isInitialized) {
            console.log('✅ Speech service is ready');
            
            // Test connection
            const connectionTest = await speechService.testConnection();
            if (connectionTest.success) {
                console.log('✅ Speech service connection test successful');
            } else {
                console.log('⚠️ Speech service connection test failed:', connectionTest.message);
            }
        } else {
            console.log('⚠️ Speech service not initialized (missing Azure credentials)');
        }
    } catch (error) {
        console.error('❌ Speech service test failed:', error.message);
    }

    // Test 5: Integration Test
    console.log('\n5️⃣ Testing Service Integration...');
    try {
        // Simulate a complete workflow
        console.log('🔄 Simulating complete workflow...');
        
        // 1. User input via session service
        const userInput = sessionService.addUserInput('Test integration workflow', 'text');
        console.log('✅ User input added to session');
        
        // 2. Process with LLM (if available)
        if (llmService.getStats().isInitialized) {
            const conversationHistory = sessionService.getOptimizedHistory();
            const llmResponse = await llmService.processText(
                'Respond briefly to: Test integration workflow',
                'default',
                conversationHistory.recent
            );
            
            // 3. Add LLM response to session
            sessionService.addLLMResponse(llmResponse.response, llmResponse.metadata);
            console.log('✅ Complete workflow test successful');
        } else {
            console.log('⚠️ Integration test skipped (LLM not available)');
        }
        
        // Final session stats
        const finalStats = sessionService.getSessionStats();
        console.log('✅ Final session stats:', finalStats);
        
    } catch (error) {
        console.error('❌ Integration test failed:', error.message);
    }

    console.log('\n🎉 Architecture test completed!');
    console.log('\n📋 Setup Instructions:');
    console.log('1. Copy .env.example to .env');
    console.log('2. Add your API keys:');
    console.log('   - AZURE_SPEECH_KEY and AZURE_SPEECH_REGION for speech services');
    console.log('   - OPENAI_API_KEY for OpenAI LLM');
    console.log('   - ANTHROPIC_API_KEY for Anthropic LLM (optional)');
    console.log('3. Run the Electron app: npm start');
}

// Handle cleanup
process.on('SIGINT', async () => {
    console.log('\n🧹 Cleaning up services...');
    try {
        await ocrService.cleanup();
        console.log('✅ Services cleaned up');
    } catch (error) {
        console.error('❌ Cleanup error:', error.message);
    }
    process.exit(0);
});

// Run the test
testServices().catch(error => {
    console.error('💥 Test suite failed:', error);
    process.exit(1);
});
