# 🚀 Migration from Gemini Live to Cluely-Inspired Architecture

## Overview

This migration replaces the Google Gemini Live API integration with a multi-component real-time audio system inspired by <PERSON><PERSON><PERSON>'s architecture. The new system provides better flexibility, cost-effectiveness, and provider independence while maintaining all core functionality.

## 🏗️ New Architecture

### **Before (Gemini Live)**
```
Microphone → Gemini Live API → Real-time Audio Processing → AI Response
```

### **After (Cluely-Inspired)**
```
┌─────────────────┐    ┌──────────────────┐    ┌─────────────────┐
│   Audio Input   │───▶│ Azure Speech STT │───▶│  Text Analysis  │
└─────────────────┘    └──────────────────┘    └─────────────────┘
                                                         │
┌─────────────────┐    ┌──────────────────┐    ┌─────────────────┐
│ Screen Capture  │───▶│  Tesseract OCR   │───▶│  LLM Provider   │
└─────────────────┘    └──────────────────┘    └─────────────────┘
                                                         │
                                               ┌─────────────────┐
                                               │   AI Response   │
                                               └─────────────────┘
```

## 🔧 New Services

### 1. **Speech Service** (`src/services/speechService.js`)
- **Replaces**: Gemini Live audio streaming
- **Uses**: Azure Speech Services for real-time STT
- **Features**: 
  - Real-time transcription
  - Speaker diarization
  - Automatic reconnection
  - Error handling and fallbacks

### 2. **LLM Service** (`src/services/llmService.js`)
- **Replaces**: Gemini AI responses
- **Uses**: Multiple providers (OpenAI, Anthropic, etc.)
- **Features**:
  - Provider-agnostic interface
  - Automatic fallback between providers
  - Profile-based system prompts
  - Conversation context management

### 3. **OCR Service** (`src/services/ocrService.js`)
- **New Feature**: Screen capture and text extraction
- **Uses**: Tesseract.js for OCR processing
- **Features**:
  - Real-time screen capture
  - Text extraction and cleaning
  - Pattern recognition (emails, URLs, code)
  - Multi-language support

### 4. **Session Service** (`src/services/sessionService.js`)
- **Replaces**: Basic conversation history
- **Features**:
  - Advanced session management
  - Conversation history optimization
  - Multi-session support
  - Memory management

## 📦 Dependencies Changed

### **Removed**
```json
"@google/genai": "^1.2.0"
```

### **Added**
```json
"microsoft-cognitiveservices-speech-sdk": "^1.34.1",
"node-record-lpcm16": "^1.0.1",
"tesseract.js": "^5.0.4",
"openai": "^4.28.4",
"@anthropic-ai/sdk": "^0.17.1",
"axios": "^1.6.7",
"dotenv": "^16.4.1"
```

## 🔑 Environment Variables

Create a `.env` file based on `.env.example`:

```bash
# Azure Speech Services (Required for audio)
AZURE_SPEECH_KEY=your_azure_speech_key_here
AZURE_SPEECH_REGION=your_azure_region_here

# LLM Provider API Keys (At least one required)
OPENAI_API_KEY=your_openai_api_key_here
ANTHROPIC_API_KEY=your_anthropic_api_key_here

# Application Settings
NODE_ENV=development
LOG_LEVEL=info
```

## 🚀 Setup Instructions

### 1. **Install Dependencies**
```bash
npm install
```

### 2. **Configure Environment**
```bash
cp .env.example .env
# Edit .env with your API keys
```

### 3. **Get Azure Speech Services**
1. Go to [Azure Portal](https://portal.azure.com)
2. Create a "Speech Services" resource
3. Copy the API key and region to your `.env` file

### 4. **Get LLM API Keys**
- **OpenAI**: Get API key from [OpenAI Platform](https://platform.openai.com/api-keys)
- **Anthropic**: Get API key from [Anthropic Console](https://console.anthropic.com/)

### 5. **Test the Setup**
```bash
node test-new-architecture.js
```

### 6. **Run the Application**
```bash
npm start
```

## 🔄 Migration Benefits

### **✅ Advantages**
1. **Provider Independence**: Not locked to Google's ecosystem
2. **Cost Effective**: Azure Speech has generous free tiers
3. **Better Performance**: Dedicated services for each component
4. **More Reliable**: Automatic fallbacks and error handling
5. **Enhanced Features**: OCR screen analysis, multi-provider LLM support
6. **Scalable**: Each service can be optimized independently

### **⚠️ Considerations**
1. **Setup Complexity**: Requires multiple API keys instead of one
2. **Latency**: Slight increase due to service orchestration
3. **Dependencies**: More external services to manage

## 🧪 Testing

The migration includes comprehensive testing:

```bash
# Test all services
node test-new-architecture.js

# Run the Electron app
npm start
```

## 🔧 Troubleshooting

### **Speech Service Issues**
- Verify Azure credentials in `.env`
- Check Azure Speech Services quota
- Ensure microphone permissions

### **LLM Service Issues**
- Verify API keys are valid
- Check API quotas and billing
- Try switching providers in the UI

### **OCR Service Issues**
- Ensure screen capture permissions
- Check if running in GUI environment
- Verify Tesseract initialization

## 📚 API Reference

### **New IPC Handlers**
```javascript
// OCR functionality
ipcMain.handle('capture-and-analyze-screen')

// Session management
ipcMain.handle('get-session-stats')
ipcMain.handle('set-session-profile', profile)

// LLM provider management
ipcMain.handle('get-available-providers')
ipcMain.handle('set-llm-provider', provider)
ipcMain.handle('test-llm-connection', provider)
```

## 🎯 Next Steps

1. **Configure your API keys** in the `.env` file
2. **Test the services** using the test script
3. **Run the application** and verify functionality
4. **Customize system prompts** for your specific use cases
5. **Monitor usage** and costs across providers

## 🆘 Support

If you encounter issues:
1. Check the console logs for detailed error messages
2. Verify all API keys are correctly configured
3. Test individual services using the test script
4. Check network connectivity and firewall settings

The new architecture maintains all existing functionality while providing greater flexibility and reliability!
