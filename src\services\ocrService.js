const { EventEmitter } = require('events');
const { screen, desktopCapturer } = require('electron');
const Tesseract = require('tesseract.js');
const fs = require('fs');
const path = require('path');

class OCRService extends EventEmitter {
    constructor() {
        super();
        this.isProcessing = false;
        this.worker = null;
        this.initializeWorker();
    }

    async initializeWorker() {
        try {
            console.log('Initializing Tesseract worker...');
            this.worker = await Tesseract.createWorker('eng');
            console.log('Tesseract worker initialized successfully');
            this.emit('status', 'OCR Service ready');
        } catch (error) {
            console.error('Failed to initialize Tesseract worker:', error);
            this.emit('error', `OCR initialization failed: ${error.message}`);
        }
    }

    async captureScreen() {
        try {
            const displays = screen.getAllDisplays();
            const primaryDisplay = displays.find(display => display.bounds.x === 0 && display.bounds.y === 0) || displays[0];

            const sources = await desktopCapturer.getSources({
                types: ['screen'],
                thumbnailSize: {
                    width: primaryDisplay.bounds.width,
                    height: primaryDisplay.bounds.height
                }
            });

            if (sources.length === 0) {
                throw new Error('No screen sources available for capture');
            }

            // Get the primary screen source
            const primarySource = sources.find(source => 
                source.display_id === primaryDisplay.id.toString()
            ) || sources[0];

            // Convert thumbnail to buffer
            const thumbnail = primarySource.thumbnail;
            const buffer = thumbnail.toPNG();

            // Save temporary file for debugging (optional)
            const tempPath = path.join(__dirname, '../../temp_screenshot.png');
            fs.writeFileSync(tempPath, buffer);

            console.log('Screen captured successfully');
            return {
                buffer,
                metadata: {
                    width: thumbnail.getSize().width,
                    height: thumbnail.getSize().height,
                    display: primaryDisplay.bounds,
                    timestamp: Date.now()
                }
            };
        } catch (error) {
            console.error('Screen capture failed:', error);
            throw new Error(`Screen capture failed: ${error.message}`);
        }
    }

    async processImage(imageBuffer, options = {}) {
        if (!this.worker) {
            throw new Error('OCR worker not initialized');
        }

        if (this.isProcessing) {
            throw new Error('OCR processing already in progress');
        }

        const startTime = Date.now();
        this.isProcessing = true;

        try {
            console.log('Starting OCR processing...');
            this.emit('processing-started');

            // Configure Tesseract options
            const tesseractOptions = {
                logger: m => {
                    if (m.status === 'recognizing text') {
                        this.emit('progress', {
                            status: m.status,
                            progress: m.progress
                        });
                    }
                },
                ...options
            };

            // Perform OCR
            const { data } = await this.worker.recognize(imageBuffer, tesseractOptions);
            
            const processingTime = Date.now() - startTime;
            
            console.log(`OCR processing completed in ${processingTime}ms`);
            console.log(`Extracted text length: ${data.text.length} characters`);
            
            const result = {
                text: data.text,
                confidence: data.confidence,
                metadata: {
                    processingTime,
                    confidence: data.confidence,
                    wordCount: data.words ? data.words.length : 0,
                    lineCount: data.lines ? data.lines.length : 0,
                    paragraphCount: data.paragraphs ? data.paragraphs.length : 0,
                    timestamp: Date.now()
                }
            };

            this.emit('processing-completed', result);
            return result;

        } catch (error) {
            console.error('OCR processing failed:', error);
            this.emit('processing-failed', error);
            throw new Error(`OCR processing failed: ${error.message}`);
        } finally {
            this.isProcessing = false;
        }
    }

    async captureAndProcess(options = {}) {
        try {
            console.log('Starting screen capture and OCR process...');
            
            // Capture screen
            const captureResult = await this.captureScreen();
            
            // Process with OCR
            const ocrResult = await this.processImage(captureResult.buffer, options);
            
            // Combine results
            const combinedResult = {
                ...ocrResult,
                metadata: {
                    ...ocrResult.metadata,
                    capture: captureResult.metadata
                }
            };

            console.log('Screen capture and OCR process completed successfully');
            return combinedResult;

        } catch (error) {
            console.error('Screen capture and OCR process failed:', error);
            throw error;
        }
    }

    async processFile(filePath, options = {}) {
        if (!fs.existsSync(filePath)) {
            throw new Error(`File not found: ${filePath}`);
        }

        try {
            const imageBuffer = fs.readFileSync(filePath);
            return await this.processImage(imageBuffer, options);
        } catch (error) {
            throw new Error(`Failed to process file ${filePath}: ${error.message}`);
        }
    }

    async enhanceImageForOCR(imageBuffer) {
        // This is a placeholder for image enhancement techniques
        // In a production environment, you might want to:
        // - Increase contrast
        // - Adjust brightness
        // - Apply noise reduction
        // - Resize for optimal OCR
        
        // For now, we'll return the original buffer
        // You can integrate image processing libraries like Sharp or Jimp here
        return imageBuffer;
    }

    async recognizeWithLanguage(imageBuffer, language = 'eng') {
        if (!this.worker) {
            await this.initializeWorker();
        }

        // Reinitialize worker with different language if needed
        if (language !== 'eng') {
            try {
                await this.worker.terminate();
                this.worker = await Tesseract.createWorker(language);
                console.log(`Tesseract worker reinitialized with language: ${language}`);
            } catch (error) {
                console.error(`Failed to initialize worker with language ${language}:`, error);
                // Fall back to English
                this.worker = await Tesseract.createWorker('eng');
            }
        }

        return await this.processImage(imageBuffer);
    }

    getStatus() {
        return {
            isInitialized: !!this.worker,
            isProcessing: this.isProcessing,
            supportedLanguages: ['eng', 'spa', 'fra', 'deu', 'ita', 'por', 'rus', 'chi_sim', 'chi_tra', 'jpn', 'kor']
        };
    }

    async cleanup() {
        if (this.worker) {
            try {
                await this.worker.terminate();
                this.worker = null;
                console.log('OCR worker terminated');
            } catch (error) {
                console.error('Error terminating OCR worker:', error);
            }
        }
    }

    // Utility method to extract specific types of content
    extractPatterns(text) {
        const patterns = {
            emails: /\b[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Z|a-z]{2,}\b/g,
            urls: /https?:\/\/(www\.)?[-a-zA-Z0-9@:%._\+~#=]{1,256}\.[a-zA-Z0-9()]{1,6}\b([-a-zA-Z0-9()@:%_\+.~#?&//=]*)/g,
            phoneNumbers: /(\+\d{1,3}[- ]?)?\d{10}/g,
            dates: /\b\d{1,2}[\/\-]\d{1,2}[\/\-]\d{2,4}\b/g,
            numbers: /\b\d+\.?\d*\b/g,
            codeBlocks: /```[\s\S]*?```|`[^`]+`/g
        };

        const extracted = {};
        
        for (const [key, pattern] of Object.entries(patterns)) {
            const matches = text.match(pattern);
            extracted[key] = matches || [];
        }

        return extracted;
    }

    // Method to clean and format extracted text
    cleanText(text) {
        return text
            .replace(/\s+/g, ' ') // Replace multiple spaces with single space
            .replace(/\n\s*\n/g, '\n') // Remove empty lines
            .trim(); // Remove leading/trailing whitespace
    }

    // Method to detect if text contains code
    detectCodeContent(text) {
        const codeIndicators = [
            /function\s+\w+\s*\(/,
            /class\s+\w+/,
            /import\s+.*from/,
            /def\s+\w+\s*\(/,
            /public\s+class/,
            /console\.log/,
            /print\s*\(/,
            /\{\s*\n.*\n\s*\}/s,
            /#include\s*</,
            /SELECT\s+.*FROM/i,
            /CREATE\s+TABLE/i
        ];

        return codeIndicators.some(pattern => pattern.test(text));
    }

    // Method to detect question content
    detectQuestionContent(text) {
        const questionIndicators = [
            /what\s+is/i,
            /how\s+do/i,
            /why\s+does/i,
            /explain/i,
            /describe/i,
            /\?\s*$/m,
            /question\s*\d+/i,
            /problem\s*\d+/i
        ];

        return questionIndicators.some(pattern => pattern.test(text));
    }
}

module.exports = new OCRService();
